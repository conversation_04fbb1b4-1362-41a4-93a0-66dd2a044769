namespace SystemReinstallHelper.Models;

/// <summary>
/// 安装进度信息
/// </summary>
public class InstallationProgress
{
    /// <summary>
    /// 当前步骤
    /// </summary>
    public InstallationStep CurrentStep { get; set; }

    /// <summary>
    /// 总体进度百分比 (0-100)
    /// </summary>
    public int OverallProgress { get; set; }

    /// <summary>
    /// 当前步骤进度百分比 (0-100)
    /// </summary>
    public int StepProgress { get; set; }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage { get; set; } = string.Empty;

    /// <summary>
    /// 详细消息
    /// </summary>
    public string DetailMessage { get; set; } = string.Empty;

    /// <summary>
    /// 是否有错误
    /// </summary>
    public bool HasError { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 预计剩余时间
    /// </summary>
    public TimeSpan? EstimatedTimeRemaining { get; set; }

    /// <summary>
    /// 是否已完成
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// 是否已取消
    /// </summary>
    public bool IsCancelled { get; set; }
}

/// <summary>
/// 安装步骤
/// </summary>
public enum InstallationStep
{
    /// <summary>
    /// 准备阶段
    /// </summary>
    Preparing,

    /// <summary>
    /// 验证镜像
    /// </summary>
    ValidatingImage,

    /// <summary>
    /// 准备磁盘
    /// </summary>
    PreparingDisk,

    /// <summary>
    /// 清理磁盘
    /// </summary>
    CleaningDisk,

    /// <summary>
    /// 挂载镜像
    /// </summary>
    MountingImage,

    /// <summary>
    /// 复制文件
    /// </summary>
    CopyingFiles,

    /// <summary>
    /// 配置系统
    /// </summary>
    ConfiguringSystem,

    /// <summary>
    /// 配置OOBE跳过
    /// </summary>
    ConfiguringOobe,

    /// <summary>
    /// 完成安装
    /// </summary>
    Finalizing,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 出错
    /// </summary>
    Error
}
