using SystemReinstallHelper.Models;

namespace SystemReinstallHelper.Services;

/// <summary>
/// 磁盘清理服务接口
/// </summary>
public interface IDiskCleanupService
{
    /// <summary>
    /// 获取所有可用磁盘信息
    /// </summary>
    /// <returns>磁盘信息列表</returns>
    Task<List<DiskInfo>> GetAvailableDisksAsync();

    /// <summary>
    /// 扫描磁盘可清理的文件
    /// </summary>
    /// <param name="diskInfo">磁盘信息</param>
    /// <returns>预计可清理的空间大小</returns>
    Task<long> ScanCleanupSizeAsync(DiskInfo diskInfo);

    /// <summary>
    /// 清理指定磁盘
    /// </summary>
    /// <param name="diskInfo">要清理的磁盘</param>
    /// <param name="progress">进度回调</param>
    /// <returns>实际清理的空间大小</returns>
    Task<long> CleanupDiskAsync(DiskInfo diskInfo, IProgress<int>? progress = null);

    /// <summary>
    /// 清理所有非系统磁盘
    /// </summary>
    /// <param name="progress">进度回调</param>
    /// <returns>总共清理的空间大小</returns>
    Task<long> CleanupAllNonSystemDisksAsync(IProgress<int>? progress = null);

    /// <summary>
    /// 获取磁盘使用情况
    /// </summary>
    /// <param name="driveLetter">驱动器号</param>
    /// <returns>磁盘信息</returns>
    Task<DiskInfo?> GetDiskInfoAsync(string driveLetter);
}
