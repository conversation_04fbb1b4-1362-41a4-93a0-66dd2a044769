namespace SystemReinstallHelper.Services;

/// <summary>
/// OOBE配置服务接口
/// </summary>
public interface IOobeConfigService
{
    /// <summary>
    /// 创建OOBE跳过配置文件
    /// </summary>
    /// <returns>配置文件路径</returns>
    Task<string> CreateOobeBypassConfigAsync();

    /// <summary>
    /// 创建自动应答文件
    /// </summary>
    /// <param name="userName">用户名</param>
    /// <param name="computerName">计算机名</param>
    /// <param name="skipPrivacySettings">是否跳过隐私设置</param>
    /// <returns>应答文件路径</returns>
    Task<string> CreateUnattendFileAsync(string userName = "User", string computerName = "PC", bool skipPrivacySettings = true);

    /// <summary>
    /// 验证配置文件是否有效
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>是否有效</returns>
    Task<bool> ValidateConfigAsync(string configPath);

    /// <summary>
    /// 应用OOBE配置到系统镜像
    /// </summary>
    /// <param name="imagePath">镜像路径</param>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>是否成功</returns>
    Task<bool> ApplyConfigToImageAsync(string imagePath, string configPath);
}
