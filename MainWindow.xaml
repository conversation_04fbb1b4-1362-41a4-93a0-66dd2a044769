<Window x:Class="SystemReinstallHelper.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ui="http://schemas.modernwpf.com/2019"
        ui:WindowHelper.UseModernWindowStyle="True"
        Title="系统重装助手" 
        Height="800" 
        Width="1200"
        MinHeight="600"
        MinWidth="900"
        WindowStartupLocation="CenterScreen"
        FontFamily="{StaticResource PrimaryFont}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                          Text="&#xE7C3;" 
                          FontFamily="Segoe MDL2 Assets" 
                          FontSize="24" 
                          Foreground="White" 
                          VerticalAlignment="Center"
                          Margin="0,0,15,0"/>
                
                <TextBlock Grid.Column="1" 
                          Text="系统重装助手" 
                          FontSize="20" 
                          FontWeight="SemiBold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="设置" 
                           Style="{StaticResource SecondaryButtonStyle}"
                           Foreground="White"
                           BorderBrush="White"
                           Margin="0,0,10,0"
                           Click="SettingsButton_Click"/>
                    <Button Content="帮助" 
                           Style="{StaticResource SecondaryButtonStyle}"
                           Foreground="White"
                           BorderBrush="White"
                           Click="HelpButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                
                <!-- 欢迎信息 -->
                <Border Style="{StaticResource StatusCardStyle}" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" 
                                  Text="&#xE8F1;" 
                                  Style="{StaticResource CardIconStyle}"
                                  Margin="0,0,20,0"/>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="欢迎使用系统重装助手" Style="{StaticResource CardTitleStyle}"/>
                            <TextBlock Text="这是一个专为小白用户设计的系统重装工具，将引导您完成整个重装过程。请按照以下步骤操作：" 
                                      Style="{StaticResource CardDescriptionStyle}"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 功能卡片区域 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 镜像扫描卡片 -->
                    <Border Grid.Row="0" Grid.Column="0" 
                           Style="{StaticResource FeatureCardStyle}"
                           MouseLeftButtonUp="ImageScanCard_Click">
                        <StackPanel>
                            <TextBlock Text="&#xE8B7;" Style="{StaticResource CardIconStyle}"/>
                            <TextBlock Text="1. 扫描系统镜像" Style="{StaticResource CardTitleStyle}"/>
                            <TextBlock Text="自动扫描并识别您的Windows安装镜像文件（ISO、WIM、ESD格式），选择最佳的挂载方式。" 
                                      Style="{StaticResource CardDescriptionStyle}"/>
                            <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                                <TextBlock Text="状态：" FontWeight="Medium" Margin="0,0,5,0"/>
                                <TextBlock x:Name="ImageScanStatusText" Text="未开始" Foreground="{StaticResource PrimaryBrush}"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 磁盘清理卡片 -->
                    <Border Grid.Row="0" Grid.Column="1" 
                           Style="{StaticResource FeatureCardStyle}"
                           MouseLeftButtonUp="DiskCleanupCard_Click">
                        <StackPanel>
                            <TextBlock Text="&#xE74D;" Style="{StaticResource CardIconStyle}"/>
                            <TextBlock Text="2. 磁盘清理" Style="{StaticResource CardTitleStyle}"/>
                            <TextBlock Text="清理非系统盘的临时文件和垃圾数据，为安装新系统腾出空间。" 
                                      Style="{StaticResource CardDescriptionStyle}"/>
                            <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                                <TextBlock Text="状态：" FontWeight="Medium" Margin="0,0,5,0"/>
                                <TextBlock x:Name="DiskCleanupStatusText" Text="未开始" Foreground="{StaticResource PrimaryBrush}"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- OOBE配置卡片 -->
                    <Border Grid.Row="1" Grid.Column="0" 
                           Style="{StaticResource FeatureCardStyle}"
                           MouseLeftButtonUp="OobeConfigCard_Click">
                        <StackPanel>
                            <TextBlock Text="&#xE713;" Style="{StaticResource CardIconStyle}"/>
                            <TextBlock Text="3. 跳过开箱体验" Style="{StaticResource CardTitleStyle}"/>
                            <TextBlock Text="自动配置跳过Windows首次启动的OOBE阶段，直接进入桌面。" 
                                      Style="{StaticResource CardDescriptionStyle}"/>
                            <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                                <TextBlock Text="状态：" FontWeight="Medium" Margin="0,0,5,0"/>
                                <TextBlock x:Name="OobeConfigStatusText" Text="未开始" Foreground="{StaticResource PrimaryBrush}"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 开始安装卡片 -->
                    <Border Grid.Row="1" Grid.Column="1" 
                           Style="{StaticResource FeatureCardStyle}"
                           MouseLeftButtonUp="StartInstallationCard_Click">
                        <StackPanel>
                            <TextBlock Text="&#xE768;" Style="{StaticResource CardIconStyle}"/>
                            <TextBlock Text="4. 开始重装" Style="{StaticResource CardTitleStyle}"/>
                            <TextBlock Text="开始系统重装过程，程序将自动完成所有配置和安装步骤。" 
                                      Style="{StaticResource CardDescriptionStyle}"/>
                            <Button x:Name="StartInstallationButton"
                                   Content="开始重装系统" 
                                   Style="{StaticResource PrimaryButtonStyle}"
                                   Margin="0,15,0,0"
                                   IsEnabled="False"
                                   Click="StartInstallationButton_Click"/>
                        </StackPanel>
                    </Border>

                    <!-- 进度显示区域 -->
                    <Border Grid.Row="2" Grid.ColumnSpan="2" 
                           Style="{StaticResource StatusCardStyle}"
                           x:Name="ProgressCard"
                           Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="安装进度" Style="{StaticResource CardTitleStyle}"/>
                            <TextBlock x:Name="ProgressStatusText" 
                                      Text="准备中..." 
                                      Style="{StaticResource CardDescriptionStyle}"
                                      Margin="0,0,0,10"/>
                            <ProgressBar x:Name="OverallProgressBar" 
                                        Height="8" 
                                        Margin="0,0,0,10"
                                        Background="#E0E0E0"
                                        Foreground="{StaticResource PrimaryBrush}"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="ProgressDetailText" 
                                          Grid.Column="0"
                                          Text="" 
                                          FontSize="12" 
                                          Foreground="#888888"/>
                                <TextBlock x:Name="ProgressPercentText" 
                                          Grid.Column="1"
                                          Text="0%" 
                                          FontSize="12" 
                                          FontWeight="Medium"
                                          Foreground="{StaticResource PrimaryBrush}"/>
                            </Grid>
                        </StackPanel>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="StatusBarText" 
                          Grid.Column="0"
                          Text="就绪" 
                          VerticalAlignment="Center"
                          FontSize="12"
                          Foreground="#666666"/>
                
                <TextBlock Grid.Column="1" 
                          Text="版本 1.0.0" 
                          VerticalAlignment="Center"
                          FontSize="12"
                          Foreground="#999999"/>
            </Grid>
        </Border>
    </Grid>
</Window>
