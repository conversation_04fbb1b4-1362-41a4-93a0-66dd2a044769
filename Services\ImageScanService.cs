using Microsoft.Extensions.Logging;
using SystemReinstallHelper.Models;
using System.IO;

namespace SystemReinstallHelper.Services;

public class ImageScanService : IImageScanService
{
    private readonly ILogger<ImageScanService> _logger;
    private readonly IWimLibService _wimLibService;

    // 常见的镜像文件搜索路径
    private readonly string[] _searchPaths = {
        @"C:\",
        @"D:\",
        @"E:\",
        @"F:\",
        Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Downloads")
    };

    // 支持的文件扩展名
    private readonly string[] _supportedExtensions = { ".iso", ".wim", ".esd" };

    public ImageScanService(ILogger<ImageScanService> logger, IWimLibService wimLibService)
    {
        _logger = logger;
        _wimLibService = wimLibService;
    }

    public async Task<List<ImageInfo>> ScanForImagesAsync()
    {
        _logger.LogInformation("开始扫描系统镜像文件");
        var images = new List<ImageInfo>();

        foreach (var searchPath in _searchPaths)
        {
            if (!Directory.Exists(searchPath))
                continue;

            try
            {
                await ScanDirectoryAsync(searchPath, images, maxDepth: 3);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "扫描路径 {SearchPath} 时发生错误", searchPath);
            }
        }

        // 验证找到的镜像文件
        var validImages = new List<ImageInfo>();
        foreach (var image in images)
        {
            var validatedImage = await ValidateImageAsync(image.FilePath);
            if (validatedImage != null)
            {
                validImages.Add(validatedImage);
            }
        }

        _logger.LogInformation("扫描完成，找到 {Count} 个有效镜像文件", validImages.Count);
        return validImages.OrderByDescending(i => i.ModifiedTime).ToList();
    }

    public async Task<ImageInfo?> ValidateImageAsync(string imagePath)
    {
        try
        {
            if (!File.Exists(imagePath))
                return null;

            var fileInfo = new FileInfo(imagePath);
            var imageType = GetImageType(fileInfo.Extension);
            
            if (imageType == ImageType.Unknown)
                return null;

            var image = new ImageInfo
            {
                FilePath = imagePath,
                FileName = fileInfo.Name,
                Type = imageType,
                Size = fileInfo.Length,
                CreatedTime = fileInfo.CreationTime,
                ModifiedTime = fileInfo.LastWriteTime
            };

            // 验证文件完整性和获取版本信息
            switch (imageType)
            {
                case ImageType.ISO:
                    image.IsValid = await ValidateIsoImageAsync(imagePath);
                    break;
                case ImageType.WIM:
                case ImageType.ESD:
                    image.IsValid = await ValidateWimImageAsync(imagePath);
                    break;
            }

            if (image.IsValid)
            {
                image.WindowsVersions = await GetWindowsVersionsAsync(imagePath);
                image.RecommendedMountMethod = DetermineBestMountMethod(image);
            }

            return image;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证镜像文件 {ImagePath} 时发生错误", imagePath);
            return new ImageInfo
            {
                FilePath = imagePath,
                FileName = Path.GetFileName(imagePath),
                IsValid = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<List<WindowsVersion>> GetWindowsVersionsAsync(string imagePath)
    {
        try
        {
            var imageType = GetImageType(Path.GetExtension(imagePath));
            
            switch (imageType)
            {
                case ImageType.ISO:
                    return await GetVersionsFromIsoAsync(imagePath);
                case ImageType.WIM:
                case ImageType.ESD:
                    return await _wimLibService.GetImageInfoAsync(imagePath);
                default:
                    return new List<WindowsVersion>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取镜像版本信息时发生错误: {ImagePath}", imagePath);
            return new List<WindowsVersion>();
        }
    }

    public MountMethod DetermineBestMountMethod(ImageInfo imageInfo)
    {
        switch (imageInfo.Type)
        {
            case ImageType.ISO:
                // ISO文件优先使用直接挂载
                return MountMethod.DirectMount;
            
            case ImageType.WIM:
            case ImageType.ESD:
                // WIM/ESD文件使用WimLib
                return MountMethod.WimLib;
            
            default:
                return MountMethod.Extract;
        }
    }

    private async Task ScanDirectoryAsync(string directoryPath, List<ImageInfo> images, int maxDepth, int currentDepth = 0)
    {
        if (currentDepth >= maxDepth)
            return;

        try
        {
            // 扫描当前目录的文件
            var files = Directory.GetFiles(directoryPath, "*.*", SearchOption.TopDirectoryOnly)
                .Where(f => _supportedExtensions.Contains(Path.GetExtension(f).ToLowerInvariant()));

            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                if (fileInfo.Length > 100 * 1024 * 1024) // 大于100MB的文件才考虑
                {
                    images.Add(new ImageInfo
                    {
                        FilePath = file,
                        FileName = fileInfo.Name,
                        Type = GetImageType(fileInfo.Extension),
                        Size = fileInfo.Length,
                        CreatedTime = fileInfo.CreationTime,
                        ModifiedTime = fileInfo.LastWriteTime
                    });
                }
            }

            // 递归扫描子目录
            var directories = Directory.GetDirectories(directoryPath);
            foreach (var directory in directories)
            {
                // 跳过系统目录和隐藏目录
                var dirInfo = new DirectoryInfo(directory);
                if (dirInfo.Attributes.HasFlag(FileAttributes.Hidden) || 
                    dirInfo.Attributes.HasFlag(FileAttributes.System))
                    continue;

                await ScanDirectoryAsync(directory, images, maxDepth, currentDepth + 1);
            }
        }
        catch (UnauthorizedAccessException)
        {
            // 忽略权限不足的目录
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "扫描目录 {DirectoryPath} 时发生错误", directoryPath);
        }
    }

    private ImageType GetImageType(string extension)
    {
        return extension.ToLowerInvariant() switch
        {
            ".iso" => ImageType.ISO,
            ".wim" => ImageType.WIM,
            ".esd" => ImageType.ESD,
            _ => ImageType.Unknown
        };
    }

    private async Task<bool> ValidateIsoImageAsync(string isoPath)
    {
        try
        {
            // 简单验证：检查文件是否可读且大小合理
            using var stream = File.OpenRead(isoPath);
            var buffer = new byte[2048];
            await stream.ReadAsync(buffer, 0, buffer.Length);
            
            // 检查ISO文件头标识
            return stream.Length > 100 * 1024 * 1024; // 至少100MB
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> ValidateWimImageAsync(string wimPath)
    {
        try
        {
            return await _wimLibService.ValidateImageAsync(wimPath);
        }
        catch
        {
            return false;
        }
    }

    private async Task<List<WindowsVersion>> GetVersionsFromIsoAsync(string isoPath)
    {
        // TODO: 实现从ISO文件中提取Windows版本信息
        // 这里需要挂载ISO并读取其中的install.wim或install.esd文件
        await Task.Delay(100); // 占位符
        
        return new List<WindowsVersion>
        {
            new WindowsVersion
            {
                Name = "Windows 11 Pro",
                Description = "从ISO文件检测到的版本",
                Version = "22H2",
                Architecture = "x64",
                Language = "zh-CN",
                Index = 1
            }
        };
    }
}
