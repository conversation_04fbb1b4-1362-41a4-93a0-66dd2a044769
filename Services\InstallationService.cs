using Microsoft.Extensions.Logging;
using SystemReinstallHelper.Models;
using System.IO;

namespace SystemReinstallHelper.Services;

public class InstallationService : IInstallationService
{
    private readonly ILogger<InstallationService> _logger;
    private readonly IWimLibService _wimLibService;
    private readonly IDiskCleanupService _diskCleanupService;
    private readonly IOobeConfigService _oobeConfigService;
    
    private InstallationProgress? _currentProgress;
    private CancellationTokenSource? _cancellationTokenSource;

    public event EventHandler<InstallationProgress>? ProgressChanged;

    public InstallationService(
        ILogger<InstallationService> logger,
        IWimLibService wimLibService,
        IDiskCleanupService diskCleanupService,
        IOobeConfigService oobeConfigService)
    {
        _logger = logger;
        _wimLibService = wimLibService;
        _diskCleanupService = diskCleanupService;
        _oobeConfigService = oobeConfigService;
    }

    public async Task<bool> StartInstallationAsync(ImageInfo imageInfo, List<DiskInfo> targetDisks)
    {
        try
        {
            _logger.LogInformation("开始系统安装");
            
            // 验证前提条件
            var (isValid, errorMessage) = await ValidatePrerequisitesAsync(imageInfo, targetDisks);
            if (!isValid)
            {
                _logger.LogError("安装前提条件验证失败: {Error}", errorMessage);
                UpdateProgress(InstallationStep.Error, 0, 0, "验证失败", errorMessage, true, errorMessage);
                return false;
            }

            _cancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = _cancellationTokenSource.Token;

            // 初始化进度
            _currentProgress = new InstallationProgress
            {
                CurrentStep = InstallationStep.Preparing,
                OverallProgress = 0,
                StepProgress = 0,
                StatusMessage = "准备安装...",
                StartTime = DateTime.Now
            };
            
            OnProgressChanged(_currentProgress);

            // 执行安装步骤
            var success = await ExecuteInstallationStepsAsync(imageInfo, targetDisks, cancellationToken);
            
            if (success)
            {
                UpdateProgress(InstallationStep.Completed, 100, 100, "安装完成", "系统重装已成功完成！", false, null, true);
                _logger.LogInformation("系统安装成功完成");
            }
            
            return success;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("安装被用户取消");
            UpdateProgress(InstallationStep.Error, 0, 0, "安装已取消", "用户取消了安装过程", false, null, false, true);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "安装过程中发生错误");
            UpdateProgress(InstallationStep.Error, 0, 0, "安装失败", $"安装过程中发生错误: {ex.Message}", true, ex.Message);
            return false;
        }
    }

    public async Task<bool> CancelInstallationAsync()
    {
        try
        {
            _logger.LogInformation("取消安装");
            _cancellationTokenSource?.Cancel();
            
            if (_currentProgress != null)
            {
                _currentProgress.IsCancelled = true;
                _currentProgress.StatusMessage = "正在取消安装...";
                OnProgressChanged(_currentProgress);
            }
            
            await Task.Delay(1000); // 给取消操作一些时间
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消安装时发生错误");
            return false;
        }
    }

    public InstallationProgress? GetCurrentProgress()
    {
        return _currentProgress;
    }

    public async Task<(bool IsValid, string? ErrorMessage)> ValidatePrerequisitesAsync(ImageInfo imageInfo, List<DiskInfo> targetDisks)
    {
        try
        {
            // 验证镜像文件
            if (!imageInfo.IsValid)
            {
                return (false, "选择的镜像文件无效");
            }

            if (!File.Exists(imageInfo.FilePath))
            {
                return (false, "镜像文件不存在");
            }

            // 验证镜像文件完整性
            var isValidImage = await _wimLibService.ValidateImageAsync(imageInfo.FilePath);
            if (!isValidImage)
            {
                return (false, "镜像文件验证失败，可能已损坏");
            }

            // 验证目标磁盘
            if (!targetDisks.Any())
            {
                return (false, "未选择目标磁盘");
            }

            // 检查磁盘空间
            var requiredSpace = imageInfo.Size * 2; // 预留双倍空间
            foreach (var disk in targetDisks)
            {
                if (disk.FreeSpace < requiredSpace)
                {
                    return (false, $"磁盘 {disk.DriveLetter} 空间不足，需要至少 {requiredSpace / (1024 * 1024 * 1024)} GB");
                }
            }

            // 检查系统权限
            if (!IsRunningAsAdministrator())
            {
                return (false, "需要管理员权限才能执行系统安装");
            }

            return (true, null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证安装前提条件时发生错误");
            return (false, $"验证过程中发生错误: {ex.Message}");
        }
    }

    private async Task<bool> ExecuteInstallationStepsAsync(ImageInfo imageInfo, List<DiskInfo> targetDisks, CancellationToken cancellationToken)
    {
        var steps = new[]
        {
            (Step: InstallationStep.ValidatingImage, Weight: 5),
            (Step: InstallationStep.PreparingDisk, Weight: 10),
            (Step: InstallationStep.CleaningDisk, Weight: 15),
            (Step: InstallationStep.MountingImage, Weight: 10),
            (Step: InstallationStep.CopyingFiles, Weight: 40),
            (Step: InstallationStep.ConfiguringSystem, Weight: 10),
            (Step: InstallationStep.ConfiguringOobe, Weight: 5),
            (Step: InstallationStep.Finalizing, Weight: 5)
        };

        int overallProgress = 0;
        
        foreach (var (step, weight) in steps)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            var success = await ExecuteStepAsync(step, imageInfo, targetDisks, cancellationToken);
            if (!success)
            {
                return false;
            }
            
            overallProgress += weight;
            UpdateProgress(step, overallProgress, 100, GetStepMessage(step), GetStepDetail(step));
            
            await Task.Delay(500, cancellationToken); // 模拟处理时间
        }

        return true;
    }

    private async Task<bool> ExecuteStepAsync(InstallationStep step, ImageInfo imageInfo, List<DiskInfo> targetDisks, CancellationToken cancellationToken)
    {
        try
        {
            UpdateProgress(step, 0, 0, GetStepMessage(step), GetStepDetail(step));
            
            switch (step)
            {
                case InstallationStep.ValidatingImage:
                    return await ValidateImageStepAsync(imageInfo, cancellationToken);
                
                case InstallationStep.PreparingDisk:
                    return await PrepareDiskStepAsync(targetDisks, cancellationToken);
                
                case InstallationStep.CleaningDisk:
                    return await CleanDiskStepAsync(targetDisks, cancellationToken);
                
                case InstallationStep.MountingImage:
                    return await MountImageStepAsync(imageInfo, cancellationToken);
                
                case InstallationStep.CopyingFiles:
                    return await CopyFilesStepAsync(imageInfo, targetDisks, cancellationToken);
                
                case InstallationStep.ConfiguringSystem:
                    return await ConfigureSystemStepAsync(targetDisks, cancellationToken);
                
                case InstallationStep.ConfiguringOobe:
                    return await ConfigureOobeStepAsync(imageInfo, cancellationToken);
                
                case InstallationStep.Finalizing:
                    return await FinalizeStepAsync(cancellationToken);
                
                default:
                    return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行安装步骤 {Step} 时发生错误", step);
            UpdateProgress(InstallationStep.Error, 0, 0, "步骤执行失败", $"执行 {GetStepMessage(step)} 时发生错误: {ex.Message}", true, ex.Message);
            return false;
        }
    }

    private async Task<bool> ValidateImageStepAsync(ImageInfo imageInfo, CancellationToken cancellationToken)
    {
        await Task.Delay(2000, cancellationToken);
        return await _wimLibService.ValidateImageAsync(imageInfo.FilePath);
    }

    private async Task<bool> PrepareDiskStepAsync(List<DiskInfo> targetDisks, CancellationToken cancellationToken)
    {
        await Task.Delay(3000, cancellationToken);
        // TODO: 实现磁盘准备逻辑（分区、格式化等）
        return true;
    }

    private async Task<bool> CleanDiskStepAsync(List<DiskInfo> targetDisks, CancellationToken cancellationToken)
    {
        var progress = new Progress<int>(p => 
        {
            if (_currentProgress != null)
            {
                _currentProgress.StepProgress = p;
                OnProgressChanged(_currentProgress);
            }
        });
        
        await _diskCleanupService.CleanupAllNonSystemDisksAsync(progress);
        return true;
    }

    private async Task<bool> MountImageStepAsync(ImageInfo imageInfo, CancellationToken cancellationToken)
    {
        await Task.Delay(2000, cancellationToken);
        // TODO: 实现镜像挂载逻辑
        return true;
    }

    private async Task<bool> CopyFilesStepAsync(ImageInfo imageInfo, List<DiskInfo> targetDisks, CancellationToken cancellationToken)
    {
        var progress = new Progress<int>(p => 
        {
            if (_currentProgress != null)
            {
                _currentProgress.StepProgress = p;
                OnProgressChanged(_currentProgress);
            }
        });
        
        // TODO: 实现文件复制逻辑
        for (int i = 0; i <= 100; i += 5)
        {
            cancellationToken.ThrowIfCancellationRequested();
            progress.Report(i);
            await Task.Delay(200, cancellationToken);
        }
        
        return true;
    }

    private async Task<bool> ConfigureSystemStepAsync(List<DiskInfo> targetDisks, CancellationToken cancellationToken)
    {
        await Task.Delay(3000, cancellationToken);
        // TODO: 实现系统配置逻辑
        return true;
    }

    private async Task<bool> ConfigureOobeStepAsync(ImageInfo imageInfo, CancellationToken cancellationToken)
    {
        await Task.Delay(1000, cancellationToken);
        var configPath = await _oobeConfigService.CreateOobeBypassConfigAsync();
        return !string.IsNullOrEmpty(configPath);
    }

    private async Task<bool> FinalizeStepAsync(CancellationToken cancellationToken)
    {
        await Task.Delay(2000, cancellationToken);
        // TODO: 实现最终化逻辑
        return true;
    }

    private void UpdateProgress(InstallationStep step, int overallProgress, int stepProgress, 
        string statusMessage, string detailMessage, bool hasError = false, string? errorMessage = null, 
        bool isCompleted = false, bool isCancelled = false)
    {
        if (_currentProgress == null) return;

        _currentProgress.CurrentStep = step;
        _currentProgress.OverallProgress = overallProgress;
        _currentProgress.StepProgress = stepProgress;
        _currentProgress.StatusMessage = statusMessage;
        _currentProgress.DetailMessage = detailMessage;
        _currentProgress.HasError = hasError;
        _currentProgress.ErrorMessage = errorMessage;
        _currentProgress.IsCompleted = isCompleted;
        _currentProgress.IsCancelled = isCancelled;

        // 计算预计剩余时间
        if (overallProgress > 0 && !isCompleted && !hasError)
        {
            var elapsed = DateTime.Now - _currentProgress.StartTime;
            var estimatedTotal = TimeSpan.FromTicks(elapsed.Ticks * 100 / overallProgress);
            _currentProgress.EstimatedTimeRemaining = estimatedTotal - elapsed;
        }

        OnProgressChanged(_currentProgress);
    }

    private void OnProgressChanged(InstallationProgress progress)
    {
        ProgressChanged?.Invoke(this, progress);
    }

    private static string GetStepMessage(InstallationStep step)
    {
        return step switch
        {
            InstallationStep.Preparing => "准备安装...",
            InstallationStep.ValidatingImage => "验证镜像文件...",
            InstallationStep.PreparingDisk => "准备磁盘...",
            InstallationStep.CleaningDisk => "清理磁盘...",
            InstallationStep.MountingImage => "挂载镜像...",
            InstallationStep.CopyingFiles => "复制系统文件...",
            InstallationStep.ConfiguringSystem => "配置系统...",
            InstallationStep.ConfiguringOobe => "配置开箱体验...",
            InstallationStep.Finalizing => "完成安装...",
            InstallationStep.Completed => "安装完成",
            InstallationStep.Error => "安装失败",
            _ => "处理中..."
        };
    }

    private static string GetStepDetail(InstallationStep step)
    {
        return step switch
        {
            InstallationStep.ValidatingImage => "正在验证镜像文件的完整性和有效性",
            InstallationStep.PreparingDisk => "正在准备目标磁盘，包括分区和格式化",
            InstallationStep.CleaningDisk => "正在清理磁盘空间，删除临时文件",
            InstallationStep.MountingImage => "正在挂载系统镜像文件",
            InstallationStep.CopyingFiles => "正在复制系统文件到目标位置",
            InstallationStep.ConfiguringSystem => "正在配置系统设置和驱动程序",
            InstallationStep.ConfiguringOobe => "正在配置跳过开箱体验设置",
            InstallationStep.Finalizing => "正在完成最后的配置和清理工作",
            _ => ""
        };
    }

    private static bool IsRunningAsAdministrator()
    {
        try
        {
            var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
            var principal = new System.Security.Principal.WindowsPrincipal(identity);
            return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
        }
        catch
        {
            return false;
        }
    }
}
