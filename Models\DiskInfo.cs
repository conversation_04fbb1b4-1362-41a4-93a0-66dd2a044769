namespace SystemReinstallHelper.Models;

/// <summary>
/// 磁盘信息
/// </summary>
public class DiskInfo
{
    /// <summary>
    /// 驱动器号
    /// </summary>
    public string DriveLetter { get; set; } = string.Empty;

    /// <summary>
    /// 卷标
    /// </summary>
    public string Label { get; set; } = string.Empty;

    /// <summary>
    /// 文件系统类型
    /// </summary>
    public string FileSystem { get; set; } = string.Empty;

    /// <summary>
    /// 总容量（字节）
    /// </summary>
    public long TotalSize { get; set; }

    /// <summary>
    /// 可用空间（字节）
    /// </summary>
    public long FreeSpace { get; set; }

    /// <summary>
    /// 已用空间（字节）
    /// </summary>
    public long UsedSpace => TotalSize - FreeSpace;

    /// <summary>
    /// 磁盘类型
    /// </summary>
    public DriveType DriveType { get; set; }

    /// <summary>
    /// 是否为系统盘
    /// </summary>
    public bool IsSystemDrive { get; set; }

    /// <summary>
    /// 是否可以清理
    /// </summary>
    public bool CanCleanup { get; set; }

    /// <summary>
    /// 清理状态
    /// </summary>
    public CleanupStatus CleanupStatus { get; set; }

    /// <summary>
    /// 预计可清理的空间（字节）
    /// </summary>
    public long EstimatedCleanupSize { get; set; }
}

/// <summary>
/// 磁盘类型
/// </summary>
public enum DriveType
{
    Unknown,
    Fixed,
    Removable,
    Network,
    CDRom,
    Ram
}

/// <summary>
/// 清理状态
/// </summary>
public enum CleanupStatus
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted,
    
    /// <summary>
    /// 扫描中
    /// </summary>
    Scanning,
    
    /// <summary>
    /// 清理中
    /// </summary>
    Cleaning,
    
    /// <summary>
    /// 已完成
    /// </summary>
    Completed,
    
    /// <summary>
    /// 出错
    /// </summary>
    Error
}
