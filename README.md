# 系统重装助手

一个专为小白用户设计的Windows系统重装工具，使用WPF和.NET 8.0开发。

## 功能特性

### 🔍 智能镜像扫描
- 自动扫描系统中的ISO、WIM、ESD镜像文件
- 智能识别Windows版本信息
- 推荐最佳挂载方式

### 🧹 磁盘清理
- 安全清理非系统盘的临时文件
- 智能识别可清理的文件类型
- 实时显示清理进度和释放空间

### ⚙️ OOBE跳过
- 自动生成跳过开箱体验的配置文件
- 支持自定义用户名和计算机名
- 自动配置隐私设置

### 📊 友好的进度显示
- 现代化卡片式界面设计
- 实时安装进度显示
- 详细的状态信息和预计剩余时间

## 系统要求

- Windows 10/11 (x64)
- .NET 8.0 Runtime
- 管理员权限
- 至少4GB可用磁盘空间

## 安装说明

1. 下载最新版本的安装包
2. 以管理员身份运行安装程序
3. 按照向导完成安装

## 使用方法

### 第一步：扫描镜像
1. 点击"扫描系统镜像"卡片
2. 程序会自动搜索系统中的镜像文件
3. 选择要安装的Windows版本

### 第二步：磁盘清理
1. 点击"磁盘清理"卡片
2. 程序会扫描非系统盘的可清理文件
3. 确认清理以释放磁盘空间

### 第三步：配置OOBE
1. 点击"跳过开箱体验"卡片
2. 程序会自动生成跳过配置
3. 可选择自定义用户设置

### 第四步：开始重装
1. 确保前三步都已完成
2. 点击"开始重装系统"按钮
3. 等待安装完成并重启

## 技术架构

### 核心技术
- **框架**: WPF + .NET 8.0
- **架构**: MVVM模式
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **UI库**: ModernWpfUI
- **镜像处理**: WimLib

### 项目结构
```
SystemReinstallHelper/
├── Models/              # 数据模型
├── Services/            # 业务服务
├── ViewModels/          # 视图模型
├── Views/               # 视图界面
├── Styles/              # 样式资源
├── Assets/              # 静态资源
└── Resources/           # WimLib库文件
```

### 主要服务
- **ImageScanService**: 镜像扫描和验证
- **DiskCleanupService**: 磁盘清理管理
- **OobeConfigService**: OOBE配置生成
- **WimLibService**: WimLib库封装
- **InstallationService**: 安装流程控制

## 开发说明

### 环境要求
- Visual Studio 2022 或 VS Code
- .NET 8.0 SDK
- Windows 10/11 开发环境

### 构建项目
```bash
# 克隆项目
git clone <repository-url>

# 进入项目目录
cd SystemReinstallHelper

# 还原NuGet包
dotnet restore

# 构建项目
dotnet build

# 运行项目
dotnet run
```

### 调试说明
1. 确保以管理员权限运行Visual Studio
2. 将Resources目录中的wimlib文件复制到输出目录
3. 准备测试用的镜像文件

## 注意事项

⚠️ **重要提醒**
- 使用前请备份重要数据
- 确保电源稳定，避免安装过程中断电
- 建议在虚拟机中测试后再在实机使用
- 仅支持UEFI启动模式的系统

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

### 提交Issue
- 详细描述问题现象
- 提供系统环境信息
- 附上相关日志文件

### 提交代码
- Fork项目到个人仓库
- 创建功能分支
- 提交Pull Request

## 更新日志

### v1.0.0 (2024-07-31)
- 初始版本发布
- 实现基本的镜像扫描功能
- 实现磁盘清理功能
- 实现OOBE跳过配置
- 实现安装进度显示

## 技术支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至技术支持邮箱

---

**免责声明**: 本工具仅供学习和研究使用，使用者需自行承担使用风险。
