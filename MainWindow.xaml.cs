using SystemReinstallHelper.ViewModels;
using System.Windows;
using System.Windows.Input;

namespace SystemReinstallHelper;

public partial class MainWindow : Window
{
    private readonly MainWindowViewModel _viewModel;

    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        DataContext = _viewModel;

        // 绑定ViewModel属性到UI元素
        BindViewModelToUI();
    }

    private void BindViewModelToUI()
    {
        // 绑定状态文本
        _viewModel.PropertyChanged += (s, e) =>
        {
            switch (e.PropertyName)
            {
                case nameof(_viewModel.StatusMessage):
                    StatusBarText.Text = _viewModel.StatusMessage;
                    break;
                case nameof(_viewModel.ImageScanStatus):
                    ImageScanStatusText.Text = _viewModel.ImageScanStatus;
                    break;
                case nameof(_viewModel.DiskCleanupStatus):
                    DiskCleanupStatusText.Text = _viewModel.DiskCleanupStatus;
                    break;
                case nameof(_viewModel.OobeConfigStatus):
                    OobeConfigStatusText.Text = _viewModel.OobeConfigStatus;
                    break;
                case nameof(_viewModel.CanStartInstallation):
                    StartInstallationButton.IsEnabled = _viewModel.CanStartInstallation;
                    break;
                case nameof(_viewModel.CurrentProgress):
                    UpdateProgressDisplay();
                    break;
                case nameof(_viewModel.IsInstalling):
                    ProgressCard.Visibility = _viewModel.IsInstalling ? Visibility.Visible : Visibility.Collapsed;
                    break;
            }
        };
    }

    private void UpdateProgressDisplay()
    {
        var progress = _viewModel.CurrentProgress;
        if (progress == null) return;

        ProgressStatusText.Text = progress.StatusMessage;
        ProgressDetailText.Text = progress.DetailMessage;
        OverallProgressBar.Value = progress.OverallProgress;
        ProgressPercentText.Text = $"{progress.OverallProgress}%";

        // 根据状态更改进度条颜色
        if (progress.HasError)
        {
            OverallProgressBar.Foreground = (System.Windows.Media.Brush)FindResource("ErrorBrush");
        }
        else if (progress.IsCompleted)
        {
            OverallProgressBar.Foreground = (System.Windows.Media.Brush)FindResource("SuccessBrush");
        }
        else
        {
            OverallProgressBar.Foreground = (System.Windows.Media.Brush)FindResource("PrimaryBrush");
        }
    }

    private async void ImageScanCard_Click(object sender, MouseButtonEventArgs e)
    {
        await _viewModel.ScanImagesCommand.ExecuteAsync(null);
    }

    private async void DiskCleanupCard_Click(object sender, MouseButtonEventArgs e)
    {
        await _viewModel.CleanupDisksCommand.ExecuteAsync(null);
    }

    private async void OobeConfigCard_Click(object sender, MouseButtonEventArgs e)
    {
        await _viewModel.ConfigureOobeCommand.ExecuteAsync(null);
    }

    private async void StartInstallationCard_Click(object sender, MouseButtonEventArgs e)
    {
        // 这里不执行安装，只是为了响应点击
    }

    private async void StartInstallationButton_Click(object sender, RoutedEventArgs e)
    {
        await _viewModel.StartInstallationCommand.ExecuteAsync(null);
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 打开设置窗口
        MessageBox.Show("设置功能正在开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void HelpButton_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 打开帮助窗口
        var helpMessage = @"系统重装助手使用说明：

1. 镜像扫描：点击第一个卡片扫描系统镜像文件
2. 磁盘清理：点击第二个卡片清理非系统盘
3. OOBE配置：点击第三个卡片配置跳过开箱体验
4. 开始重装：完成前三步后点击开始重装按钮

注意事项：
- 请确保已备份重要数据
- 重装过程中请勿断电或强制关机
- 建议在重装前关闭杀毒软件

如需更多帮助，请联系技术支持。";

        MessageBox.Show(helpMessage, "帮助", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    protected override void OnClosed(EventArgs e)
    {
        // 清理资源
        base.OnClosed(e);
    }
}
