using SystemReinstallHelper.Models;

namespace SystemReinstallHelper.Services;

/// <summary>
/// 镜像扫描服务接口
/// </summary>
public interface IImageScanService
{
    /// <summary>
    /// 扫描系统中的镜像文件
    /// </summary>
    /// <returns>找到的镜像文件列表</returns>
    Task<List<ImageInfo>> ScanForImagesAsync();

    /// <summary>
    /// 验证镜像文件是否有效
    /// </summary>
    /// <param name="imagePath">镜像文件路径</param>
    /// <returns>镜像信息，如果无效则返回null</returns>
    Task<ImageInfo?> ValidateImageAsync(string imagePath);

    /// <summary>
    /// 获取镜像文件中的Windows版本信息
    /// </summary>
    /// <param name="imagePath">镜像文件路径</param>
    /// <returns>Windows版本列表</returns>
    Task<List<WindowsVersion>> GetWindowsVersionsAsync(string imagePath);

    /// <summary>
    /// 确定最佳的挂载方式
    /// </summary>
    /// <param name="imageInfo">镜像信息</param>
    /// <returns>推荐的挂载方式</returns>
    MountMethod DetermineBestMountMethod(ImageInfo imageInfo);
}
