@echo off
echo 系统重装助手启动脚本
echo ========================

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，正在启动应用程序...
    echo.
) else (
    echo 错误：需要管理员权限才能运行此应用程序
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

REM 检查.NET 8.0运行时
echo 检查.NET 8.0运行时...
dotnet --version >nul 2>&1
if %errorLevel% == 0 (
    echo .NET运行时检查通过
) else (
    echo 错误：未检测到.NET 8.0运行时
    echo 请从以下地址下载并安装：
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

REM 检查wimlib文件
if exist "Resources\wimlib-imagex.exe" (
    echo WimLib文件检查通过
) else (
    echo 警告：未找到WimLib文件，某些功能可能无法正常工作
    echo 请确保Resources目录中包含wimlib-imagex.exe文件
)

echo.
echo 正在启动系统重装助手...
echo.

REM 启动应用程序
dotnet run

if %errorLevel% == 0 (
    echo.
    echo 应用程序已正常退出
) else (
    echo.
    echo 应用程序异常退出，错误代码：%errorLevel%
    echo 请检查日志文件获取详细信息
)

echo.
pause
