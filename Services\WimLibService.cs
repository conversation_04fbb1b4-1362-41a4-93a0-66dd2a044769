using Microsoft.Extensions.Logging;
using SystemReinstallHelper.Models;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace SystemReinstallHelper.Services;

public class WimLibService : IWimLibService
{
    private readonly ILogger<WimLibService> _logger;
    private readonly string _wimLibPath;

    public WimLibService(ILogger<WimLibService> logger)
    {
        _logger = logger;
        
        // 查找wimlib-imagex.exe的路径
        _wimLibPath = FindWimLibExecutable();
        
        if (string.IsNullOrEmpty(_wimLibPath))
        {
            throw new FileNotFoundException("未找到wimlib-imagex.exe，请确保Resources目录中包含该文件");
        }
        
        _logger.LogInformation("WimLib路径: {WimLibPath}", _wimLibPath);
    }

    public async Task<bool> ValidateImageAsync(string imagePath)
    {
        try
        {
            _logger.LogInformation("验证镜像文件: {ImagePath}", imagePath);
            
            var result = await ExecuteWimLibCommandAsync($"info \"{imagePath}\"");
            return result.ExitCode == 0 && !string.IsNullOrEmpty(result.Output);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证镜像文件时发生错误: {ImagePath}", imagePath);
            return false;
        }
    }

    public async Task<List<WindowsVersion>> GetImageInfoAsync(string imagePath)
    {
        try
        {
            _logger.LogInformation("获取镜像信息: {ImagePath}", imagePath);
            
            var result = await ExecuteWimLibCommandAsync($"info \"{imagePath}\"");
            if (result.ExitCode != 0)
            {
                _logger.LogError("获取镜像信息失败: {Error}", result.Error);
                return new List<WindowsVersion>();
            }

            return ParseImageInfo(result.Output);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取镜像信息时发生错误: {ImagePath}", imagePath);
            return new List<WindowsVersion>();
        }
    }

    public async Task<bool> MountImageAsync(string imagePath, string mountPoint, int imageIndex = 1)
    {
        try
        {
            _logger.LogInformation("挂载镜像: {ImagePath} 到 {MountPoint}, 索引: {Index}", 
                imagePath, mountPoint, imageIndex);
            
            // 确保挂载点目录存在
            Directory.CreateDirectory(mountPoint);
            
            var result = await ExecuteWimLibCommandAsync($"mountrw \"{imagePath}\" {imageIndex} \"{mountPoint}\"");
            
            if (result.ExitCode == 0)
            {
                _logger.LogInformation("镜像挂载成功");
                return true;
            }
            else
            {
                _logger.LogError("镜像挂载失败: {Error}", result.Error);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "挂载镜像时发生错误");
            return false;
        }
    }

    public async Task<bool> UnmountImageAsync(string mountPoint, bool commitChanges = false)
    {
        try
        {
            _logger.LogInformation("卸载镜像: {MountPoint}, 提交更改: {Commit}", mountPoint, commitChanges);
            
            var command = commitChanges ? $"unmount \"{mountPoint}\" --commit" : $"unmount \"{mountPoint}\"";
            var result = await ExecuteWimLibCommandAsync(command);
            
            if (result.ExitCode == 0)
            {
                _logger.LogInformation("镜像卸载成功");
                return true;
            }
            else
            {
                _logger.LogError("镜像卸载失败: {Error}", result.Error);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "卸载镜像时发生错误");
            return false;
        }
    }

    public async Task<bool> ApplyImageAsync(string imagePath, string targetPath, int imageIndex = 1, IProgress<int>? progress = null)
    {
        try
        {
            _logger.LogInformation("应用镜像: {ImagePath} 到 {TargetPath}, 索引: {Index}", 
                imagePath, targetPath, imageIndex);
            
            // 确保目标目录存在
            Directory.CreateDirectory(targetPath);
            
            var result = await ExecuteWimLibCommandAsync($"apply \"{imagePath}\" {imageIndex} \"{targetPath}\"", progress);
            
            if (result.ExitCode == 0)
            {
                _logger.LogInformation("镜像应用成功");
                return true;
            }
            else
            {
                _logger.LogError("镜像应用失败: {Error}", result.Error);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用镜像时发生错误");
            return false;
        }
    }

    public async Task<bool> CaptureImageAsync(string sourcePath, string imagePath, string imageName, IProgress<int>? progress = null)
    {
        try
        {
            _logger.LogInformation("捕获镜像: {SourcePath} 到 {ImagePath}, 名称: {ImageName}", 
                sourcePath, imagePath, imageName);
            
            var result = await ExecuteWimLibCommandAsync($"capture \"{sourcePath}\" \"{imagePath}\" \"{imageName}\"", progress);
            
            if (result.ExitCode == 0)
            {
                _logger.LogInformation("镜像捕获成功");
                return true;
            }
            else
            {
                _logger.LogError("镜像捕获失败: {Error}", result.Error);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "捕获镜像时发生错误");
            return false;
        }
    }

    public string GetVersion()
    {
        try
        {
            var result = ExecuteWimLibCommandAsync("--version").Result;
            return result.Output.Trim();
        }
        catch
        {
            return "Unknown";
        }
    }

    private string FindWimLibExecutable()
    {
        // 查找wimlib-imagex.exe的可能路径
        var possiblePaths = new[]
        {
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "wimlib-imagex.exe"),
            Path.Combine(Environment.CurrentDirectory, "Resources", "wimlib-imagex.exe"),
            Path.Combine(Directory.GetCurrentDirectory(), "Resources", "wimlib-imagex.exe"),
            "wimlib-imagex.exe" // 系统PATH中
        };

        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        return string.Empty;
    }

    private async Task<(int ExitCode, string Output, string Error)> ExecuteWimLibCommandAsync(string arguments, IProgress<int>? progress = null)
    {
        var processInfo = new ProcessStartInfo
        {
            FileName = _wimLibPath,
            Arguments = arguments,
            UseShellExecute = false,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            CreateNoWindow = true,
            StandardOutputEncoding = Encoding.UTF8,
            StandardErrorEncoding = Encoding.UTF8
        };

        var outputBuilder = new StringBuilder();
        var errorBuilder = new StringBuilder();

        using var process = new Process { StartInfo = processInfo };
        
        process.OutputDataReceived += (sender, e) =>
        {
            if (e.Data != null)
            {
                outputBuilder.AppendLine(e.Data);
                
                // 尝试解析进度信息
                if (progress != null)
                {
                    var progressMatch = Regex.Match(e.Data, @"(\d+)%");
                    if (progressMatch.Success && int.TryParse(progressMatch.Groups[1].Value, out int progressValue))
                    {
                        progress.Report(progressValue);
                    }
                }
            }
        };

        process.ErrorDataReceived += (sender, e) =>
        {
            if (e.Data != null)
            {
                errorBuilder.AppendLine(e.Data);
            }
        };

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        await process.WaitForExitAsync();

        return (process.ExitCode, outputBuilder.ToString(), errorBuilder.ToString());
    }

    private List<WindowsVersion> ParseImageInfo(string output)
    {
        var versions = new List<WindowsVersion>();
        
        try
        {
            var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            WindowsVersion? currentVersion = null;
            
            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                
                if (trimmedLine.StartsWith("Index:"))
                {
                    if (currentVersion != null)
                    {
                        versions.Add(currentVersion);
                    }
                    
                    currentVersion = new WindowsVersion();
                    var indexMatch = Regex.Match(trimmedLine, @"Index:\s*(\d+)");
                    if (indexMatch.Success && int.TryParse(indexMatch.Groups[1].Value, out int index))
                    {
                        currentVersion.Index = index;
                    }
                }
                else if (currentVersion != null)
                {
                    if (trimmedLine.StartsWith("Name:"))
                    {
                        currentVersion.Name = trimmedLine.Substring(5).Trim();
                    }
                    else if (trimmedLine.StartsWith("Description:"))
                    {
                        currentVersion.Description = trimmedLine.Substring(12).Trim();
                    }
                    else if (trimmedLine.StartsWith("Architecture:"))
                    {
                        currentVersion.Architecture = trimmedLine.Substring(13).Trim();
                    }
                    else if (trimmedLine.StartsWith("Version:"))
                    {
                        currentVersion.Version = trimmedLine.Substring(8).Trim();
                    }
                    else if (trimmedLine.StartsWith("Default Language:"))
                    {
                        currentVersion.Language = trimmedLine.Substring(17).Trim();
                    }
                }
            }
            
            // 添加最后一个版本
            if (currentVersion != null)
            {
                versions.Add(currentVersion);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析镜像信息时发生错误");
        }
        
        return versions;
    }
}
