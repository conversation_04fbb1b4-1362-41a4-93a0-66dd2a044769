<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 卡片容器样式 -->
    <Style x:Key="CardContainerStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource CardBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="Padding" Value="20" />
        <Setter Property="Margin" Value="10" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 功能卡片样式 -->
    <Style x:Key="FeatureCardStyle" TargetType="Border" BasedOn="{StaticResource CardContainerStyle}">
        <Setter Property="MinHeight" Value="200" />
        <Setter Property="Cursor" Value="Hand" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F0F8FF" />
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 状态卡片样式 -->
    <Style x:Key="StatusCardStyle" TargetType="Border" BasedOn="{StaticResource CardContainerStyle}">
        <Setter Property="MinHeight" Value="120" />
    </Style>

    <!-- 卡片标题样式 -->
    <Style x:Key="CardTitleStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}" />
        <Setter Property="FontSize" Value="18" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Foreground" Value="#333333" />
        <Setter Property="Margin" Value="0,0,0,10" />
    </Style>

    <!-- 卡片描述样式 -->
    <Style x:Key="CardDescriptionStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Foreground" Value="#666666" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="LineHeight" Value="20" />
    </Style>

    <!-- 卡片图标样式 -->
    <Style x:Key="CardIconStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe MDL2 Assets" />
        <Setter Property="FontSize" Value="32" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Margin" Value="0,0,0,15" />
    </Style>

</ResourceDictionary>
