using Microsoft.Extensions.Logging;
using SystemReinstallHelper.Models;
using System.IO;

namespace SystemReinstallHelper.Services;

public class DiskCleanupService : IDiskCleanupService
{
    private readonly ILogger<DiskCleanupService> _logger;

    // 可清理的文件类型和目录
    private readonly string[] _cleanupFilePatterns = {
        "*.tmp", "*.temp", "*.log", "*.bak", "*.old", "*.cache",
        "Thumbs.db", "desktop.ini", "*.dmp", "*.chk"
    };

    private readonly string[] _cleanupDirectories = {
        "Temp", "tmp", "Cache", "Logs", "Backup", "Recycle.Bin", "$RECYCLE.BIN"
    };

    public DiskCleanupService(ILogger<DiskCleanupService> logger)
    {
        _logger = logger;
    }

    public async Task<List<DiskInfo>> GetAvailableDisksAsync()
    {
        _logger.LogInformation("获取可用磁盘信息");
        var disks = new List<DiskInfo>();

        try
        {
            var drives = DriveInfo.GetDrives();
            var systemDrive = Path.GetPathRoot(Environment.SystemDirectory)?.TrimEnd('\\');

            foreach (var drive in drives)
            {
                if (drive.DriveType != System.IO.DriveType.Fixed || !drive.IsReady)
                    continue;

                var diskInfo = new DiskInfo
                {
                    DriveLetter = drive.Name.TrimEnd('\\'),
                    Label = drive.VolumeLabel,
                    FileSystem = drive.DriveFormat,
                    TotalSize = drive.TotalSize,
                    FreeSpace = drive.AvailableFreeSpace,
                    DriveType = ConvertDriveType(drive.DriveType),
                    IsSystemDrive = string.Equals(drive.Name.TrimEnd('\\'), systemDrive, StringComparison.OrdinalIgnoreCase),
                    CleanupStatus = CleanupStatus.NotStarted
                };

                // 非系统盘才允许清理
                diskInfo.CanCleanup = !diskInfo.IsSystemDrive;

                if (diskInfo.CanCleanup)
                {
                    diskInfo.EstimatedCleanupSize = await ScanCleanupSizeAsync(diskInfo);
                }

                disks.Add(diskInfo);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取磁盘信息时发生错误");
        }

        return disks;
    }

    public async Task<long> ScanCleanupSizeAsync(DiskInfo diskInfo)
    {
        if (!diskInfo.CanCleanup)
            return 0;

        _logger.LogInformation("扫描磁盘 {DriveLetter} 的可清理空间", diskInfo.DriveLetter);
        
        try
        {
            diskInfo.CleanupStatus = CleanupStatus.Scanning;
            long totalSize = 0;

            var rootPath = diskInfo.DriveLetter + "\\";
            
            // 扫描根目录下的临时文件
            totalSize += await ScanDirectoryForCleanupAsync(rootPath, false);

            // 扫描特定的清理目录
            foreach (var cleanupDir in _cleanupDirectories)
            {
                var dirPath = Path.Combine(rootPath, cleanupDir);
                if (Directory.Exists(dirPath))
                {
                    totalSize += await ScanDirectoryForCleanupAsync(dirPath, true);
                }
            }

            diskInfo.CleanupStatus = CleanupStatus.NotStarted;
            return totalSize;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫描磁盘 {DriveLetter} 时发生错误", diskInfo.DriveLetter);
            diskInfo.CleanupStatus = CleanupStatus.Error;
            return 0;
        }
    }

    public async Task<long> CleanupDiskAsync(DiskInfo diskInfo, IProgress<int>? progress = null)
    {
        if (!diskInfo.CanCleanup)
            return 0;

        _logger.LogInformation("开始清理磁盘 {DriveLetter}", diskInfo.DriveLetter);
        
        try
        {
            diskInfo.CleanupStatus = CleanupStatus.Cleaning;
            long totalCleaned = 0;
            int currentProgress = 0;

            var rootPath = diskInfo.DriveLetter + "\\";
            var totalDirectories = _cleanupDirectories.Length + 1; // +1 for root directory

            // 清理根目录下的临时文件
            progress?.Report(currentProgress);
            totalCleaned += await CleanupDirectoryAsync(rootPath, false);
            currentProgress = (int)((1.0 / totalDirectories) * 100);
            progress?.Report(currentProgress);

            // 清理特定的清理目录
            for (int i = 0; i < _cleanupDirectories.Length; i++)
            {
                var cleanupDir = _cleanupDirectories[i];
                var dirPath = Path.Combine(rootPath, cleanupDir);
                
                if (Directory.Exists(dirPath))
                {
                    totalCleaned += await CleanupDirectoryAsync(dirPath, true);
                }

                currentProgress = (int)(((i + 2.0) / totalDirectories) * 100);
                progress?.Report(currentProgress);
            }

            diskInfo.CleanupStatus = CleanupStatus.Completed;
            progress?.Report(100);
            
            _logger.LogInformation("磁盘 {DriveLetter} 清理完成，清理了 {Size} 字节", 
                diskInfo.DriveLetter, totalCleaned);
            
            return totalCleaned;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理磁盘 {DriveLetter} 时发生错误", diskInfo.DriveLetter);
            diskInfo.CleanupStatus = CleanupStatus.Error;
            return 0;
        }
    }

    public async Task<long> CleanupAllNonSystemDisksAsync(IProgress<int>? progress = null)
    {
        _logger.LogInformation("开始清理所有非系统磁盘");
        
        var disks = await GetAvailableDisksAsync();
        var cleanupDisks = disks.Where(d => d.CanCleanup).ToList();
        
        if (!cleanupDisks.Any())
        {
            _logger.LogInformation("没有找到可清理的磁盘");
            return 0;
        }

        long totalCleaned = 0;
        
        for (int i = 0; i < cleanupDisks.Count; i++)
        {
            var disk = cleanupDisks[i];
            var diskProgress = new Progress<int>(p =>
            {
                var overallProgress = (int)((i * 100 + p) / (double)cleanupDisks.Count);
                progress?.Report(overallProgress);
            });

            totalCleaned += await CleanupDiskAsync(disk, diskProgress);
        }

        progress?.Report(100);
        _logger.LogInformation("所有磁盘清理完成，总共清理了 {Size} 字节", totalCleaned);
        
        return totalCleaned;
    }

    public async Task<DiskInfo?> GetDiskInfoAsync(string driveLetter)
    {
        try
        {
            var drive = new DriveInfo(driveLetter);
            if (!drive.IsReady)
                return null;

            var systemDrive = Path.GetPathRoot(Environment.SystemDirectory)?.TrimEnd('\\');
            
            var diskInfo = new DiskInfo
            {
                DriveLetter = drive.Name.TrimEnd('\\'),
                Label = drive.VolumeLabel,
                FileSystem = drive.DriveFormat,
                TotalSize = drive.TotalSize,
                FreeSpace = drive.AvailableFreeSpace,
                DriveType = ConvertDriveType(drive.DriveType),
                IsSystemDrive = string.Equals(drive.Name.TrimEnd('\\'), systemDrive, StringComparison.OrdinalIgnoreCase),
                CleanupStatus = CleanupStatus.NotStarted
            };

            diskInfo.CanCleanup = !diskInfo.IsSystemDrive;
            
            if (diskInfo.CanCleanup)
            {
                diskInfo.EstimatedCleanupSize = await ScanCleanupSizeAsync(diskInfo);
            }

            return diskInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取磁盘 {DriveLetter} 信息时发生错误", driveLetter);
            return null;
        }
    }

    private async Task<long> ScanDirectoryForCleanupAsync(string directoryPath, bool recursive)
    {
        long totalSize = 0;

        try
        {
            // 扫描匹配的文件
            foreach (var pattern in _cleanupFilePatterns)
            {
                var files = Directory.GetFiles(directoryPath, pattern, 
                    recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly);
                
                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.Exists && !fileInfo.Attributes.HasFlag(FileAttributes.System))
                        {
                            totalSize += fileInfo.Length;
                        }
                    }
                    catch
                    {
                        // 忽略无法访问的文件
                    }
                }
            }

            await Task.Delay(1); // 避免阻塞UI
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "扫描目录 {DirectoryPath} 时发生错误", directoryPath);
        }

        return totalSize;
    }

    private async Task<long> CleanupDirectoryAsync(string directoryPath, bool recursive)
    {
        long totalCleaned = 0;

        try
        {
            // 删除匹配的文件
            foreach (var pattern in _cleanupFilePatterns)
            {
                var files = Directory.GetFiles(directoryPath, pattern, 
                    recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly);
                
                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.Exists && !fileInfo.Attributes.HasFlag(FileAttributes.System))
                        {
                            var size = fileInfo.Length;
                            fileInfo.Delete();
                            totalCleaned += size;
                        }
                    }
                    catch
                    {
                        // 忽略无法删除的文件
                    }
                }
            }

            // 如果是递归清理，尝试删除空目录
            if (recursive)
            {
                try
                {
                    var directories = Directory.GetDirectories(directoryPath, "*", SearchOption.AllDirectories)
                        .OrderByDescending(d => d.Length); // 从最深的目录开始

                    foreach (var dir in directories)
                    {
                        try
                        {
                            if (Directory.Exists(dir) && !Directory.EnumerateFileSystemEntries(dir).Any())
                            {
                                Directory.Delete(dir);
                            }
                        }
                        catch
                        {
                            // 忽略无法删除的目录
                        }
                    }
                }
                catch
                {
                    // 忽略目录删除错误
                }
            }

            await Task.Delay(1); // 避免阻塞UI
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清理目录 {DirectoryPath} 时发生错误", directoryPath);
        }

        return totalCleaned;
    }

    private static Models.DriveType ConvertDriveType(System.IO.DriveType driveType)
    {
        return driveType switch
        {
            System.IO.DriveType.Fixed => Models.DriveType.Fixed,
            System.IO.DriveType.Removable => Models.DriveType.Removable,
            System.IO.DriveType.Network => Models.DriveType.Network,
            System.IO.DriveType.CDRom => Models.DriveType.CDRom,
            System.IO.DriveType.Ram => Models.DriveType.Ram,
            _ => Models.DriveType.Unknown
        };
    }
}
