using SystemReinstallHelper.Models;

namespace SystemReinstallHelper.Services;

/// <summary>
/// 安装服务接口
/// </summary>
public interface IInstallationService
{
    /// <summary>
    /// 进度变化事件
    /// </summary>
    event EventHandler<InstallationProgress>? ProgressChanged;

    /// <summary>
    /// 开始系统安装
    /// </summary>
    /// <param name="imageInfo">镜像信息</param>
    /// <param name="targetDisks">目标磁盘列表</param>
    /// <returns>是否成功开始</returns>
    Task<bool> StartInstallationAsync(ImageInfo imageInfo, List<DiskInfo> targetDisks);

    /// <summary>
    /// 取消安装
    /// </summary>
    /// <returns>是否成功取消</returns>
    Task<bool> CancelInstallationAsync();

    /// <summary>
    /// 获取当前安装进度
    /// </summary>
    /// <returns>安装进度</returns>
    InstallationProgress? GetCurrentProgress();

    /// <summary>
    /// 验证安装前提条件
    /// </summary>
    /// <param name="imageInfo">镜像信息</param>
    /// <param name="targetDisks">目标磁盘列表</param>
    /// <returns>验证结果和错误信息</returns>
    Task<(bool IsValid, string? ErrorMessage)> ValidatePrerequisitesAsync(ImageInfo imageInfo, List<DiskInfo> targetDisks);
}
