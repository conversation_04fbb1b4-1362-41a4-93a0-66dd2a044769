wimlib (meaning all programs, scripts, libraries, documentation, and other files
that are part of the wimlib project -- not just the "libwim" library) may be
redistributed and/or modified under the terms of the GNU General Public License;
either version 3 of the License, or (at your option) any later version.  A copy
of this license can be found in the file COPYING.GPLv3.

Also, when not prohibited by a third-party software license, libwim (the library
portion of wimlib) may be redistributed and/or modified under the terms of the
GNU Lesser General Public License; either version 3 of the License, or (at your
option) any later version.  A copy of this license can be found in the file
COPYING.LGPLv3.  This is offered as a "dual license", meaning that you can
choose either this LGPLv3+ option or the above-mentioned GPLv3+ option.

In either case there is NO WARRANTY, to the extent permitted by law.

--------------------------------------------------------------------------------

NOTE! The reason for the "when not prohibited by a third-party software license"
condition on the LGPL option for libwim is that libwim can optionally be linked
to the third-party library "libntfs-3g", which is licensed under the GPL.
Usually the GPL is interpreted in a way that means that any binary that uses a
GPL library must be licensed under the GPL as well, not (for example) the LGPL.

Therefore, if your build of libwim links to libntfs-3g, then you can't choose
the LGPL option.  You may choose the LGPL option for Windows builds of libwim,
since they don't link to libntfs-3g.  Likewise, you may choose the LGPL option
for UNIX builds of libwim that were built with './configure --without-ntfs-3g'.
