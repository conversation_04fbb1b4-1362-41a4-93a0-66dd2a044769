using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using SystemReinstallHelper.Models;
using SystemReinstallHelper.Services;
using System.Collections.ObjectModel;
using System.Windows;

namespace SystemReinstallHelper.ViewModels;

public partial class MainWindowViewModel : ObservableObject
{
    private readonly IImageScanService _imageScanService;
    private readonly IDiskCleanupService _diskCleanupService;
    private readonly IOobeConfigService _oobeConfigService;
    private readonly IInstallationService _installationService;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private string _imageScanStatus = "未开始";

    [ObservableProperty]
    private string _diskCleanupStatus = "未开始";

    [ObservableProperty]
    private string _oobeConfigStatus = "未开始";

    [ObservableProperty]
    private bool _canStartInstallation = false;

    [ObservableProperty]
    private bool _isInstalling = false;

    [ObservableProperty]
    private InstallationProgress? _currentProgress;

    [ObservableProperty]
    private ObservableCollection<ImageInfo> _availableImages = new();

    [ObservableProperty]
    private ObservableCollection<DiskInfo> _availableDisks = new();

    [ObservableProperty]
    private ImageInfo? _selectedImage;

    public MainWindowViewModel(
        IImageScanService imageScanService,
        IDiskCleanupService diskCleanupService,
        IOobeConfigService oobeConfigService,
        IInstallationService installationService,
        ILogger<MainWindowViewModel> logger)
    {
        _imageScanService = imageScanService;
        _diskCleanupService = diskCleanupService;
        _oobeConfigService = oobeConfigService;
        _installationService = installationService;
        _logger = logger;

        // 订阅安装进度事件
        _installationService.ProgressChanged += OnInstallationProgressChanged;
    }

    [RelayCommand]
    public async Task ScanImagesAsync()
    {
        try
        {
            ImageScanStatus = "扫描中...";
            StatusMessage = "正在扫描系统镜像文件...";

            var images = await _imageScanService.ScanForImagesAsync();
            AvailableImages.Clear();
            
            foreach (var image in images)
            {
                AvailableImages.Add(image);
            }

            if (AvailableImages.Any())
            {
                ImageScanStatus = $"找到 {AvailableImages.Count} 个镜像";
                SelectedImage = AvailableImages.First();
                StatusMessage = "镜像扫描完成";
            }
            else
            {
                ImageScanStatus = "未找到镜像";
                StatusMessage = "未找到可用的系统镜像文件";
            }

            CheckCanStartInstallation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫描镜像时发生错误");
            ImageScanStatus = "扫描失败";
            StatusMessage = $"扫描镜像失败: {ex.Message}";
            MessageBox.Show($"扫描镜像时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    public async Task CleanupDisksAsync()
    {
        try
        {
            DiskCleanupStatus = "扫描中...";
            StatusMessage = "正在扫描磁盘信息...";

            var disks = await _diskCleanupService.GetAvailableDisksAsync();
            AvailableDisks.Clear();
            
            foreach (var disk in disks.Where(d => d.CanCleanup))
            {
                AvailableDisks.Add(disk);
            }

            if (AvailableDisks.Any())
            {
                DiskCleanupStatus = $"找到 {AvailableDisks.Count} 个可清理磁盘";
                StatusMessage = "磁盘扫描完成";
            }
            else
            {
                DiskCleanupStatus = "无需清理";
                StatusMessage = "没有找到需要清理的磁盘";
            }

            CheckCanStartInstallation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫描磁盘时发生错误");
            DiskCleanupStatus = "扫描失败";
            StatusMessage = $"扫描磁盘失败: {ex.Message}";
            MessageBox.Show($"扫描磁盘时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    public async Task ConfigureOobeAsync()
    {
        try
        {
            OobeConfigStatus = "配置中...";
            StatusMessage = "正在配置OOBE跳过设置...";

            await _oobeConfigService.CreateOobeBypassConfigAsync();
            
            OobeConfigStatus = "配置完成";
            StatusMessage = "OOBE跳过配置已完成";

            CheckCanStartInstallation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置OOBE时发生错误");
            OobeConfigStatus = "配置失败";
            StatusMessage = $"配置OOBE失败: {ex.Message}";
            MessageBox.Show($"配置OOBE时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    public async Task StartInstallationAsync()
    {
        if (SelectedImage == null)
        {
            MessageBox.Show("请先选择要安装的系统镜像", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        var result = MessageBox.Show(
            "确定要开始重装系统吗？此操作将清除目标磁盘上的所有数据！", 
            "确认", 
            MessageBoxButton.YesNo, 
            MessageBoxImage.Question);

        if (result != MessageBoxResult.Yes)
            return;

        try
        {
            IsInstalling = true;
            CanStartInstallation = false;
            StatusMessage = "正在开始系统重装...";

            await _installationService.StartInstallationAsync(SelectedImage, AvailableDisks.ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始安装时发生错误");
            StatusMessage = $"安装失败: {ex.Message}";
            MessageBox.Show($"开始安装时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            IsInstalling = false;
            CheckCanStartInstallation();
        }
    }

    private void OnInstallationProgressChanged(object? sender, InstallationProgress progress)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            CurrentProgress = progress;
            StatusMessage = progress.StatusMessage;

            if (progress.IsCompleted)
            {
                IsInstalling = false;
                StatusMessage = "系统重装完成！";
                MessageBox.Show("系统重装已完成！请重启计算机。", "完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else if (progress.HasError)
            {
                IsInstalling = false;
                CheckCanStartInstallation();
                MessageBox.Show($"安装过程中发生错误：{progress.ErrorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        });
    }

    private void CheckCanStartInstallation()
    {
        CanStartInstallation = !IsInstalling && 
                              AvailableImages.Any() && 
                              ImageScanStatus.Contains("找到") &&
                              OobeConfigStatus == "配置完成";
    }
}
