using SystemReinstallHelper.Models;

namespace SystemReinstallHelper.Services;

/// <summary>
/// WimLib服务接口
/// </summary>
public interface IWimLibService
{
    /// <summary>
    /// 验证镜像文件是否有效
    /// </summary>
    /// <param name="imagePath">镜像文件路径</param>
    /// <returns>是否有效</returns>
    Task<bool> ValidateImageAsync(string imagePath);

    /// <summary>
    /// 获取镜像文件信息
    /// </summary>
    /// <param name="imagePath">镜像文件路径</param>
    /// <returns>Windows版本列表</returns>
    Task<List<WindowsVersion>> GetImageInfoAsync(string imagePath);

    /// <summary>
    /// 挂载镜像文件
    /// </summary>
    /// <param name="imagePath">镜像文件路径</param>
    /// <param name="mountPoint">挂载点</param>
    /// <param name="imageIndex">镜像索引</param>
    /// <returns>是否成功</returns>
    Task<bool> MountImageAsync(string imagePath, string mountPoint, int imageIndex = 1);

    /// <summary>
    /// 卸载镜像
    /// </summary>
    /// <param name="mountPoint">挂载点</param>
    /// <param name="commitChanges">是否提交更改</param>
    /// <returns>是否成功</returns>
    Task<bool> UnmountImageAsync(string mountPoint, bool commitChanges = false);

    /// <summary>
    /// 应用镜像到目标位置
    /// </summary>
    /// <param name="imagePath">镜像文件路径</param>
    /// <param name="targetPath">目标路径</param>
    /// <param name="imageIndex">镜像索引</param>
    /// <param name="progress">进度回调</param>
    /// <returns>是否成功</returns>
    Task<bool> ApplyImageAsync(string imagePath, string targetPath, int imageIndex = 1, IProgress<int>? progress = null);

    /// <summary>
    /// 捕获镜像
    /// </summary>
    /// <param name="sourcePath">源路径</param>
    /// <param name="imagePath">目标镜像路径</param>
    /// <param name="imageName">镜像名称</param>
    /// <param name="progress">进度回调</param>
    /// <returns>是否成功</returns>
    Task<bool> CaptureImageAsync(string sourcePath, string imagePath, string imageName, IProgress<int>? progress = null);

    /// <summary>
    /// 获取WimLib版本信息
    /// </summary>
    /// <returns>版本字符串</returns>
    string GetVersion();
}
