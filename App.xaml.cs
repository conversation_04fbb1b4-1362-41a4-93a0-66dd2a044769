using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SystemReinstallHelper.Services;
using SystemReinstallHelper.ViewModels;
using System.Windows;

namespace SystemReinstallHelper;

public partial class App : Application
{
    private ServiceProvider? _serviceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // 配置依赖注入
        var services = new ServiceCollection();
        ConfigureServices(services);
        _serviceProvider = services.BuildServiceProvider();

        // 创建并显示主窗口
        var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // 日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // 业务服务
        services.AddSingleton<IImageScanService, ImageScanService>();
        services.AddSingleton<IDiskCleanupService, DiskCleanupService>();
        services.AddSingleton<IOobeConfigService, OobeConfigService>();
        services.AddSingleton<IWimLibService, WimLibService>();
        services.AddSingleton<IInstallationService, InstallationService>();

        // ViewModels
        services.AddTransient<MainWindowViewModel>();

        // Views
        services.AddTransient<MainWindow>();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _serviceProvider?.Dispose();
        base.OnExit(e);
    }
}
