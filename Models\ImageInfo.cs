namespace SystemReinstallHelper.Models;

/// <summary>
/// 镜像文件信息
/// </summary>
public class ImageInfo
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型 (ISO, WIM, ESD)
    /// </summary>
    public ImageType Type { get; set; }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedTime { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 推荐的挂载方式
    /// </summary>
    public MountMethod RecommendedMountMethod { get; set; }

    /// <summary>
    /// 镜像内包含的Windows版本信息
    /// </summary>
    public List<WindowsVersion> WindowsVersions { get; set; } = new();

    /// <summary>
    /// 错误信息（如果有）
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 镜像文件类型
/// </summary>
public enum ImageType
{
    Unknown,
    ISO,
    WIM,
    ESD
}

/// <summary>
/// 挂载方式
/// </summary>
public enum MountMethod
{
    /// <summary>
    /// 直接挂载
    /// </summary>
    DirectMount,
    
    /// <summary>
    /// 使用WimLib
    /// </summary>
    WimLib,
    
    /// <summary>
    /// 解压缩
    /// </summary>
    Extract
}

/// <summary>
/// Windows版本信息
/// </summary>
public class WindowsVersion
{
    /// <summary>
    /// 版本名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 版本描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 架构 (x86, x64, ARM64)
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// 语言
    /// </summary>
    public string Language { get; set; } = string.Empty;

    /// <summary>
    /// 镜像索引
    /// </summary>
    public int Index { get; set; }
}
