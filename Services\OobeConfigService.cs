using Microsoft.Extensions.Logging;
using System.IO;
using System.Text;
using System.Xml;

namespace SystemReinstallHelper.Services;

public class OobeConfigService : IOobeConfigService
{
    private readonly ILogger<OobeConfigService> _logger;
    private readonly string _configDirectory;

    public OobeConfigService(ILogger<OobeConfigService> logger)
    {
        _logger = logger;
        _configDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
            "SystemReinstallHelper", "Configs");
        
        Directory.CreateDirectory(_configDirectory);
    }

    public async Task<string> CreateOobeBypassConfigAsync()
    {
        _logger.LogInformation("创建OOBE跳过配置文件");
        
        try
        {
            var configPath = Path.Combine(_configDirectory, "oobe_bypass.xml");
            var unattendContent = GenerateUnattendXml();
            
            await File.WriteAllTextAsync(configPath, unattendContent, Encoding.UTF8);
            
            _logger.LogInformation("OOBE跳过配置文件已创建: {ConfigPath}", configPath);
            return configPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建OOBE跳过配置文件时发生错误");
            throw;
        }
    }

    public async Task<string> CreateUnattendFileAsync(string userName = "User", string computerName = "PC", bool skipPrivacySettings = true)
    {
        _logger.LogInformation("创建自动应答文件");
        
        try
        {
            var configPath = Path.Combine(_configDirectory, "unattend.xml");
            var unattendContent = GenerateCustomUnattendXml(userName, computerName, skipPrivacySettings);
            
            await File.WriteAllTextAsync(configPath, unattendContent, Encoding.UTF8);
            
            _logger.LogInformation("自动应答文件已创建: {ConfigPath}", configPath);
            return configPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建自动应答文件时发生错误");
            throw;
        }
    }

    public async Task<bool> ValidateConfigAsync(string configPath)
    {
        try
        {
            if (!File.Exists(configPath))
                return false;

            var content = await File.ReadAllTextAsync(configPath);
            
            // 验证XML格式
            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(content);
            
            // 检查是否包含必要的节点
            var unattendNode = xmlDoc.SelectSingleNode("//unattend");
            if (unattendNode == null)
                return false;

            _logger.LogInformation("配置文件验证成功: {ConfigPath}", configPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证配置文件时发生错误: {ConfigPath}", configPath);
            return false;
        }
    }

    public async Task<bool> ApplyConfigToImageAsync(string imagePath, string configPath)
    {
        _logger.LogInformation("应用OOBE配置到镜像: {ImagePath}", imagePath);
        
        try
        {
            if (!await ValidateConfigAsync(configPath))
            {
                _logger.LogError("配置文件无效: {ConfigPath}", configPath);
                return false;
            }

            // TODO: 实现将配置文件应用到镜像的逻辑
            // 这通常需要挂载镜像，然后将unattend.xml复制到适当的位置
            
            await Task.Delay(100); // 占位符
            
            _logger.LogInformation("OOBE配置已成功应用到镜像");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用OOBE配置到镜像时发生错误");
            return false;
        }
    }

    private string GenerateUnattendXml()
    {
        return @"<?xml version=""1.0"" encoding=""utf-8""?>
<unattend xmlns=""urn:schemas-microsoft-com:unattend"">
    <settings pass=""oobeSystem"">
        <component name=""Microsoft-Windows-Shell-Setup"" processorArchitecture=""amd64"" publicKeyToken=""31bf3856ad364e35"" language=""neutral"" versionScope=""nonSxS"" xmlns:wcm=""http://schemas.microsoft.com/WMIConfig/2002/State"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
            <OOBE>
                <HideEULAPage>true</HideEULAPage>
                <HideLocalAccountScreen>true</HideLocalAccountScreen>
                <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>
                <HideOnlineAccountScreens>true</HideOnlineAccountScreens>
                <HideWirelessSetupInOOBE>true</HideWirelessSetupInOOBE>
                <NetworkLocation>Home</NetworkLocation>
                <ProtectYourPC>1</ProtectYourPC>
                <SkipUserOOBE>true</SkipUserOOBE>
                <SkipMachineOOBE>true</SkipMachineOOBE>
            </OOBE>
            <UserAccounts>
                <LocalAccounts>
                    <LocalAccount wcm:action=""add"">
                        <Password>
                            <Value></Value>
                            <PlainText>true</PlainText>
                        </Password>
                        <Description>Default User Account</Description>
                        <DisplayName>User</DisplayName>
                        <Group>Administrators</Group>
                        <Name>User</Name>
                    </LocalAccount>
                </LocalAccounts>
            </UserAccounts>
            <AutoLogon>
                <Password>
                    <Value></Value>
                    <PlainText>true</PlainText>
                </Password>
                <Enabled>true</Enabled>
                <LogonCount>1</LogonCount>
                <Username>User</Username>
            </AutoLogon>
            <FirstLogonCommands>
                <SynchronousCommand wcm:action=""add"">
                    <CommandLine>cmd /c reg add ""HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"" /v ""EnableLUA"" /t REG_DWORD /d 0 /f</CommandLine>
                    <Description>Disable UAC</Description>
                    <Order>1</Order>
                </SynchronousCommand>
                <SynchronousCommand wcm:action=""add"">
                    <CommandLine>cmd /c reg add ""HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"" /v ""ConsentPromptBehaviorAdmin"" /t REG_DWORD /d 0 /f</CommandLine>
                    <Description>Disable UAC Prompt</Description>
                    <Order>2</Order>
                </SynchronousCommand>
            </FirstLogonCommands>
        </component>
    </settings>
    <settings pass=""specialize"">
        <component name=""Microsoft-Windows-Shell-Setup"" processorArchitecture=""amd64"" publicKeyToken=""31bf3856ad364e35"" language=""neutral"" versionScope=""nonSxS"" xmlns:wcm=""http://schemas.microsoft.com/WMIConfig/2002/State"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
            <ComputerName>PC</ComputerName>
        </component>
        <component name=""Microsoft-Windows-Deployment"" processorArchitecture=""amd64"" publicKeyToken=""31bf3856ad364e35"" language=""neutral"" versionScope=""nonSxS"" xmlns:wcm=""http://schemas.microsoft.com/WMIConfig/2002/State"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
            <RunSynchronous>
                <RunSynchronousCommand wcm:action=""add"">
                    <Order>1</Order>
                    <Path>cmd /c reg add ""HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"" /v ""EnableLUA"" /t REG_DWORD /d 0 /f</Path>
                </RunSynchronousCommand>
            </RunSynchronous>
        </component>
    </settings>
</unattend>";
    }

    private string GenerateCustomUnattendXml(string userName, string computerName, bool skipPrivacySettings)
    {
        var sb = new StringBuilder();
        sb.AppendLine(@"<?xml version=""1.0"" encoding=""utf-8""?>");
        sb.AppendLine(@"<unattend xmlns=""urn:schemas-microsoft-com:unattend"">");
        
        // OOBE System Pass
        sb.AppendLine(@"    <settings pass=""oobeSystem"">");
        sb.AppendLine(@"        <component name=""Microsoft-Windows-Shell-Setup"" processorArchitecture=""amd64"" publicKeyToken=""31bf3856ad364e35"" language=""neutral"" versionScope=""nonSxS"" xmlns:wcm=""http://schemas.microsoft.com/WMIConfig/2002/State"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">");
        
        // OOBE Settings
        sb.AppendLine(@"            <OOBE>");
        sb.AppendLine(@"                <HideEULAPage>true</HideEULAPage>");
        sb.AppendLine(@"                <HideLocalAccountScreen>true</HideLocalAccountScreen>");
        sb.AppendLine(@"                <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>");
        sb.AppendLine(@"                <HideOnlineAccountScreens>true</HideOnlineAccountScreens>");
        sb.AppendLine(@"                <HideWirelessSetupInOOBE>true</HideWirelessSetupInOOBE>");
        sb.AppendLine(@"                <NetworkLocation>Home</NetworkLocation>");
        sb.AppendLine(@"                <ProtectYourPC>1</ProtectYourPC>");
        sb.AppendLine(@"                <SkipUserOOBE>true</SkipUserOOBE>");
        sb.AppendLine(@"                <SkipMachineOOBE>true</SkipMachineOOBE>");
        sb.AppendLine(@"            </OOBE>");
        
        // User Accounts
        sb.AppendLine(@"            <UserAccounts>");
        sb.AppendLine(@"                <LocalAccounts>");
        sb.AppendLine(@"                    <LocalAccount wcm:action=""add"">");
        sb.AppendLine(@"                        <Password>");
        sb.AppendLine(@"                            <Value></Value>");
        sb.AppendLine(@"                            <PlainText>true</PlainText>");
        sb.AppendLine(@"                        </Password>");
        sb.AppendLine($@"                        <Description>Default User Account</Description>");
        sb.AppendLine($@"                        <DisplayName>{userName}</DisplayName>");
        sb.AppendLine(@"                        <Group>Administrators</Group>");
        sb.AppendLine($@"                        <Name>{userName}</Name>");
        sb.AppendLine(@"                    </LocalAccount>");
        sb.AppendLine(@"                </LocalAccounts>");
        sb.AppendLine(@"            </UserAccounts>");
        
        // Auto Logon
        sb.AppendLine(@"            <AutoLogon>");
        sb.AppendLine(@"                <Password>");
        sb.AppendLine(@"                    <Value></Value>");
        sb.AppendLine(@"                    <PlainText>true</PlainText>");
        sb.AppendLine(@"                </Password>");
        sb.AppendLine(@"                <Enabled>true</Enabled>");
        sb.AppendLine(@"                <LogonCount>1</LogonCount>");
        sb.AppendLine($@"                <Username>{userName}</Username>");
        sb.AppendLine(@"            </AutoLogon>");
        
        // First Logon Commands
        sb.AppendLine(@"            <FirstLogonCommands>");
        sb.AppendLine(@"                <SynchronousCommand wcm:action=""add"">");
        sb.AppendLine(@"                    <CommandLine>cmd /c reg add ""HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"" /v ""EnableLUA"" /t REG_DWORD /d 0 /f</CommandLine>");
        sb.AppendLine(@"                    <Description>Disable UAC</Description>");
        sb.AppendLine(@"                    <Order>1</Order>");
        sb.AppendLine(@"                </SynchronousCommand>");
        
        if (skipPrivacySettings)
        {
            sb.AppendLine(@"                <SynchronousCommand wcm:action=""add"">");
            sb.AppendLine(@"                    <CommandLine>cmd /c reg add ""HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Privacy"" /v ""TailoredExperiencesWithDiagnosticDataEnabled"" /t REG_DWORD /d 0 /f</CommandLine>");
            sb.AppendLine(@"                    <Description>Disable Privacy Settings</Description>");
            sb.AppendLine(@"                    <Order>2</Order>");
            sb.AppendLine(@"                </SynchronousCommand>");
        }
        
        sb.AppendLine(@"            </FirstLogonCommands>");
        sb.AppendLine(@"        </component>");
        sb.AppendLine(@"    </settings>");
        
        // Specialize Pass
        sb.AppendLine(@"    <settings pass=""specialize"">");
        sb.AppendLine(@"        <component name=""Microsoft-Windows-Shell-Setup"" processorArchitecture=""amd64"" publicKeyToken=""31bf3856ad364e35"" language=""neutral"" versionScope=""nonSxS"" xmlns:wcm=""http://schemas.microsoft.com/WMIConfig/2002/State"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">");
        sb.AppendLine($@"            <ComputerName>{computerName}</ComputerName>");
        sb.AppendLine(@"        </component>");
        sb.AppendLine(@"    </settings>");
        
        sb.AppendLine(@"</unattend>");
        
        return sb.ToString();
    }
}
